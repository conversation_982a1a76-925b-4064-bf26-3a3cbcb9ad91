{"appId": "6847d44bc63a45e78048bb9e", "appName": "polaris-bui-components", "components": [{"appId": "6847d44bc63a45e78048bb9e", "assetId": "polaris-button-tabs", "code": "https://cdnfile.corp.kuaishou.com/kc/files/a/airstar/polaris-bui-components-polaris-button-tabs-5wc3p0jz/polaris-button-tabs.cjs.js", "syncCode": "new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('[lux-info]polaris-button-tabs in iframe'); topWindow = topWindow.parent; } const lockName = '_polaris-button-tabs_Lock'; const eventName = '_polaris-button-tabs_LockReleased'; const finalStatusFlag = '_polaris-button-tabs_FinalStatus'; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === 'success') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('script'); script.src = 'https://cdnfile.corp.kuaishou.com/kc/files/a/airstar/polaris-bui-components-polaris-button-tabs-5wc3p0jz/polaris-button-tabs.cjs.js'; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('[lux-info]polaris-button-tabs consumed', perfLoss, 'ms to finish load, timeout:', timeout); resolved = true; if (timeout) { releaseLock('failed'); reject('[lux-info]polaris-button-tabs Load timeout'); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: 'airstar-load-custom-components-per-loss', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: 'polaris-button-tabs', perfLoss, }, }); releaseLock('success'); delete topWindow[lockName]; resolve(); } }; window['_polaris-button-tabs-tabs.cjs_resolver'] = resolveWrapper; topWindow['_polaris-button-tabs-tabs.cjs_resolver'] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('[lux-info]polaris-button-tabs load failed', e); clearTimeout(timeoutId); releaseLock('failed'); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('[lux-info]polaris-button-tabs Another instance is already running, waiting for lock release.'); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === 'success') { console.log('[lux-info]polaris-button-tabs resolve directly'); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('[lux-info]polaris-button-tabs acquire lock again'); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('[lux-info]polaris-button-tabs 执行垫片逻辑时报错', error); reject(error); } });", "currentVersionNo": 57, "componentName": "polaris-button-tabs", "buildHash": "i6qc12zf", "deployTime": "2025-07-29T12:57:04.804Z"}], "deployTime": "2025-07-29T12:57:04.804Z", "buildHash": "i6qc12zf", "totalComponents": 1, "buildType": "vite-umd", "pipelineVersion": "2.0"}