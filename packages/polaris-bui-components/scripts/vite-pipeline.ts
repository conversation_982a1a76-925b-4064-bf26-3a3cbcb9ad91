#!/usr/bin/env node

/**
 * Polaris BUI Components Vite Pipeline 脚本
 * 构建 -> 上传 -> API调用的完整流程
 */

import { existsSync, writeFileSync, readFileSync, statSync, readdirSync } from 'node:fs';
import * as path from 'node:path';
import { fileURLToPath } from 'node:url';
import { execSync } from 'node:child_process';
import * as crypto from 'node:crypto';
import { build } from 'vite';
import { createUMDConfig } from '../vite.umd.config.js';
import { createPolarisUMDUploader, type PolarisUMDUploader } from './utils/cdn-uploader.js';
import { createPolarisAPIClient, getComponentNameZh, getComponentAssetId, type PolarisAPIClient } from './utils/api-client.js';

function generateId(length: number = 8): string {
  return Math.random().toString(36).substring(2, 2 + length);
}

/**
 * 获取Git分支信息
 */
function getGitBranch(): string {
  try {
    return execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf-8' }).trim();
  } catch (error) {
    console.warn('[Git] 无法获取Git分支信息，使用默认值');
    return 'unknown-branch';
  }
}

/**
 * 获取Git提交者信息
 */
function getGitCommitter(): string {
  try {
    return execSync('git log -1 --pretty=format:"%cn"', { encoding: 'utf-8' }).trim();
  } catch (error) {
    console.warn('[Git] 无法获取Git提交者信息，使用默认值');
    return 'unknown-committer';
  }
}

/**
 * 计算文件内容hash
 */
function calculateFileHash(filePath: string): string {
  try {
    const content = readFileSync(filePath);
    return crypto.createHash('md5').update(content).digest('hex');
  } catch (error) {
    console.warn(`[Hash] 无法计算文件hash: ${filePath}`, error);
    return generateId(16); // 返回随机hash作为fallback
  }
}

// 获取当前脚本所在目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.join(__dirname, '..');

interface ComponentInfo {
  name: string;
  path: string;
  entry: string;
  type: 'base' | 'service' | 'table' | 'filter' | 'complex';
}

interface BuildResult {
  componentName: string;
  success: boolean;
  filePath?: string;
  fileSize?: number;
  buildTime?: number;
  error?: string;
}

interface UploadResult {
  componentName: string;
  success: boolean;
  cdnUrl?: string;
  buildHash?: string;
  filePath?: string; // 添加文件路径用于Hash计算
  error?: string;
}

interface PipelineOptions {
  skipBuild?: boolean;
  skipUpload?: boolean;
  skipApi?: boolean;
  components?: string[];
  filter?: string;
  dryRun?: boolean;
  mock?: boolean;
}

class PolarisVitePipeline {
  private appConfig: any;
  private components: ComponentInfo[] = [];
  private buildResults: BuildResult[] = [];
  private uploadResults: UploadResult[] = [];
  private cdnUploader: PolarisUMDUploader;
  private apiClient: PolarisAPIClient;

  constructor(private options: PipelineOptions = {}) {
    this.loadAppConfig();

    // 初始化CDN上传器和API客户端
    // CDN使用mock模式：当指定mock、dryRun、skipUpload时
    const useMockCDN = this.options.mock || this.options.dryRun || this.options.skipUpload;
    // API使用mock模式：只有在dryRun时才使用模拟API，其他情况使用真实API
    const useMockAPI = this.options.dryRun;

    console.log(`[Pipeline] 初始化模式: mock=${this.options.mock}, dryRun=${this.options.dryRun}, skipUpload=${this.options.skipUpload}, useMockCDN=${useMockCDN}, useMockAPI=${useMockAPI}`);

    this.cdnUploader = createPolarisUMDUploader(useMockCDN);
    this.apiClient = createPolarisAPIClient(useMockAPI);
  }

  /**
   * 加载应用配置
   */
  private loadAppConfig(): void {
    try {
      const configPath = path.join(projectRoot, '.airstar.config.json');
      if (existsSync(configPath)) {
        this.appConfig = JSON.parse(readFileSync(configPath, 'utf-8'));
        console.log('[Config] 配置文件加载成功');
      } else {
        // 使用新的appId：6847d44bc63a45e78048bb9e
        this.appConfig = {
          appId: '6847d44bc63a45e78048bb9e',
          appName: 'polaris-bui-components',
          syncLoadTimeoutMs: 3000
        };
        console.warn('[Config] 未找到配置文件，使用默认配置');
      }

      // 确保 appId 正确
      this.appConfig.appId = '6847d44bc63a45e78048bb9e';

    } catch (error) {
      console.error('[Config] 配置文件加载失败:', error);
      throw new Error('配置文件加载失败');
    }
  }

  /**
   * 扫描组件目录
   */
  private scanComponents(): void {
    console.log('🔍 扫描组件目录...');

    const componentsDir = path.join(projectRoot, 'components');
    if (!existsSync(componentsDir)) {
      throw new Error('组件目录不存在');
    }

    const entries = readdirSync(componentsDir);

    for (const entry of entries) {
      const componentPath = path.join(componentsDir, entry);
      const stat = statSync(componentPath);

      if (!stat.isDirectory()) {
        continue;
      }

      // 检查是否为base子组件 - 只构建middleware组件
      if (entry === 'base') {
        console.log(`📁 扫描base目录: ${componentPath} (仅构建middleware)`);
        const middlewarePath = path.join(componentPath, 'middleware');
        if (existsSync(middlewarePath) && statSync(middlewarePath).isDirectory()) {
          const componentInfo = this.createComponentInfo('base/middleware', middlewarePath);
          if (componentInfo) {
            console.log(`📦 添加base组件: base-middleware (包含所有base子组件)`);
            this.components.push(componentInfo);
          }
        } else {
          console.warn(`⚠️  base/middleware 目录不存在，跳过base组件构建`);
        }
        continue;
      }

      // 平级组件
      const componentInfo = this.createComponentInfo(entry, componentPath);
      if (componentInfo) {
        this.components.push(componentInfo);
      }
    }

    // 应用过滤器
    this.applyFilters();

    console.log(`📦 发现 ${this.components.length} 个组件:`);
    this.components.forEach(comp => {
      console.log(`  - ${comp.type}/${comp.name}`);
    });
  }

  /**
   * 创建组件信息对象
   */
  private createComponentInfo(relativePath: string, fullPath: string): ComponentInfo | null {
    const pathParts = relativePath.split(/[\/\\]/);
    const componentName = pathParts.join('-');

    // 查找入口文件
    const possibleEntries = [
      'entry.tsx',
      'entry.ts',
      'index.tsx',
      'index.ts',
      'src/index.tsx',
      'src/index.ts'
    ];

    let entryFile: string | null = null;
    for (const entry of possibleEntries) {
      const entryPath = path.join(fullPath, entry);
      if (existsSync(entryPath)) {
        entryFile = entry;
        break;
      }
    }

    if (!entryFile) {
      console.warn(`[Scan] 组件 ${componentName} 未找到入口文件，跳过`);
      return null;
    }

    // 确定组件类型
    const type = this.determineComponentType(relativePath);

    return {
      name: componentName,
      type,
      entry: path.join(fullPath, entryFile),
      path: fullPath
    };
  }

  /**
   * 确定组件类型
   */
  private determineComponentType(relativePath: string): ComponentInfo['type'] {
    if (relativePath.includes('base/')) return 'base';
    if (relativePath.includes('service')) return 'service';
    if (relativePath.includes('table')) return 'table';
    if (relativePath.includes('filter')) return 'filter';
    return 'complex';
  }

  /**
   * 应用组件过滤器
   */
  private applyFilters(): void {
    let filtered = this.components;

    // 按名称过滤（支持正则表达式）
    if (this.options.filter) {
      const regex = new RegExp(this.options.filter, 'i');
      filtered = filtered.filter(comp => regex.test(comp.name));
      console.log(`[Filter] 按名称过滤 '${this.options.filter}': ${filtered.length} 个组件`);
    }

    // 按指定组件名过滤
    if (this.options.components && this.options.components.length > 0) {
      filtered = filtered.filter(comp => this.options.components!.includes(comp.name));
      console.log(`[Filter] 按组件名过滤: ${filtered.length} 个组件`);
    }

    this.components = filtered;
  }

  /**
   * 步骤1: 构建UMD组件
   */
  async runBuild(): Promise<void> {
    if (this.options.skipBuild) {
      console.log('⏭️  跳过构建步骤，尝试使用现有构建产物');
      this.loadExistingBuildResults();
      return;
    }

    console.log('🔨 开始构建UMD组件...');

    if (this.components.length === 0) {
      throw new Error('没有可构建的组件');
    }

    // 清空输出目录（只在第一次）
    const outputDir = path.join(projectRoot, 'dist');
    if (existsSync(outputDir)) {
      console.log('[Build] 清空输出目录...');
      const { rmSync } = await import('fs');
      rmSync(outputDir, { recursive: true, force: true });
    }

    // 顺序构建每个组件（避免并行冲突）
    for (const component of this.components) {
      const result = await this.buildComponent(component);
      this.buildResults.push(result);
    }

    // 输出构建统计
    const successCount = this.buildResults.filter(r => r.success).length;
    const failureCount = this.buildResults.filter(r => !r.success).length;

    console.log(`[Build] 构建完成: ${successCount} 成功, ${failureCount} 失败`);

    if (failureCount > 0) {
      console.log('\n[Build] 失败的组件:');
      this.buildResults.filter(r => !r.success).forEach(r => {
        console.log(`  - ${r.componentName}: ${r.error}`);
      });
    }
  }



  /**
   * 构建单个组件
   */
  private async buildComponent(component: ComponentInfo): Promise<BuildResult> {
    console.log(`[Build] 构建组件: ${component.name}`);
        const startTime = Date.now();

        try {
      // 使用UMD配置
      const config = createUMDConfig([component]);

      if (typeof config === 'function') {
        throw new Error('配置不能是函数形式');
      }

      // 解析配置（如果是Promise则等待）
      const resolvedConfig = await Promise.resolve(config);

      // 修改配置以避免清空输出目录（关键！）
      const buildConfig = {
        ...resolvedConfig,
        build: {
          ...resolvedConfig.build,
          emptyOutDir: false, // 关键设置：不清空输出目录，避免覆盖之前构建的文件
        },
      };

      // 使用修改后的配置进行构建
      await build(buildConfig);

      const buildTime = Date.now() - startTime;
      const outputDir = path.join(projectRoot, 'dist');
      // UMD输出文件名
      const outputFile = path.join(outputDir, `${component.name}.umd.js`);

      let fileSize = 0;

      // 等待一小段时间确保文件写入完成
      await new Promise(resolve => setTimeout(resolve, 100));

      if (existsSync(outputFile)) {
        const stat = statSync(outputFile);
        fileSize = stat.size;
        console.log(`[Build] ✅ ${component.name} - ${(fileSize / 1024).toFixed(2)}KB (${buildTime}ms)`);
      } else {
        // 检查dist目录中是否有其他可能的输出文件
        const distFiles = existsSync(outputDir) ? readdirSync(outputDir) : [];
        const possibleFiles = distFiles.filter(f => f.includes(component.name));

        if (possibleFiles.length > 0) {
          console.log(`[Build] ⚠️  ${component.name} 预期文件不存在，但找到相关文件: ${possibleFiles.join(', ')}`);
          // 使用找到的第一个相关文件
          const actualFile = path.join(outputDir, possibleFiles[0]);
          const stat = statSync(actualFile);
          fileSize = stat.size;
          console.log(`[Build] ✅ ${component.name} - 使用 ${possibleFiles[0]} - ${(fileSize / 1024).toFixed(2)}KB (${buildTime}ms)`);

          return {
            componentName: component.name,
            success: true,
            filePath: actualFile,
            fileSize,
            buildTime,
          };
        } else {
          throw new Error(`构建后文件不存在: ${outputFile}，dist目录文件: ${distFiles.join(', ')}`);
        }
      }

      return {
        componentName: component.name,
            success: true,
        filePath: outputFile,
        fileSize,
            buildTime,
      };

        } catch (error: any) {
          const buildTime = Date.now() - startTime;
      console.error(`[Build] ❌ ${component.name} 构建失败 (${buildTime}ms):`, error.message);

      return {
        componentName: component.name,
            success: false,
            buildTime,
            error: error.message || '未知错误',
      };
    }
  }

  /**
   * 加载现有构建产物
   */
  private loadExistingBuildResults(): void {
    const distPath = path.join(projectRoot, 'dist');

    if (!existsSync(distPath)) {
      console.warn('[Build] 构建输出目录不存在，无法加载现有构建产物');
      return;
    }

    try {
      const files = readdirSync(distPath);
      // 使用UMD文件扩展名
      const extension = '.umd.js';
      const targetFiles = files.filter((file: string) => file.endsWith(extension));

      console.log(`[Build] 在 ${distPath} 中找到 ${targetFiles.length} 个 ${extension} 文件`);

      for (const file of targetFiles) {
        const componentName = file.replace(extension, '');
        const filePath = path.join(distPath, file);
        const stats = statSync(filePath);

        this.buildResults.push({
          componentName,
          success: true,
          filePath,
          fileSize: stats.size
        });

        console.log(`[Build] 📁 加载现有构建产物: ${componentName} - ${(stats.size / 1024).toFixed(2)}KB`);
      }

      console.log(`[Build] 成功加载 ${this.buildResults.length} 个现有构建产物`);

    } catch (error) {
      console.error('[Build] 加载现有构建产物失败:', error);
    }
  }

  /**
   * 步骤2: 上传到CDN
   */
  async uploadToCDN(): Promise<void> {
    // dry-run 模式：只构建，不上传，不调用API
    if (this.options.dryRun) {
      console.log('⏭️  跳过上传步骤（干跑模式：只构建）');

      // 生成模拟的CDN URLs用于环境文件生成
      for (const build of this.buildResults) {
        if (build.success && build.filePath) {
          const buildHash = generateId(8);
          const extension = 'umd.js';
          this.uploadResults.push({
            componentName: build.componentName,
            success: true,
            cdnUrl: `https://static.yximgs.com/airstar/components/${build.componentName}/${buildHash}.${extension}`,
            buildHash: buildHash,
            filePath: build.filePath // 记录文件路径用于Hash计算
          });
        }
      }
      return;
    }

    // skip-upload 模式：不上传，但生成模拟CDN URLs用于API调用
    if (this.options.skipUpload) {
      console.log('⏭️  跳过上传步骤，生成模拟CDN URLs');

      // 生成模拟的CDN URLs用于API调用
      for (const build of this.buildResults) {
        if (build.success && build.filePath) {
          const buildHash = generateId(8);
          const extension = 'umd.js';
          this.uploadResults.push({
            componentName: build.componentName,
            success: true,
            cdnUrl: `https://static.yximgs.com/airstar/components/${build.componentName}/${buildHash}.${extension}`,
            buildHash: buildHash,
            filePath: build.filePath // 记录文件路径用于Hash计算
          });
        }
      }
      return;
    }

    console.log('📤 开始上传组件到CDN...');

    // 准备上传文件列表
    const uploadFiles = this.buildResults
      .filter(build => build.success && build.filePath)
      .map(build => ({
        filePath: build.filePath!,
        componentName: build.componentName,
        buildHash: generateId(8),
      }));

    if (uploadFiles.length === 0) {
      console.warn('[Upload] 没有可上传的文件');
      return;
    }

    try {
      // 使用CDN上传器进行批量上传
      const results = await this.cdnUploader.uploadBatch(uploadFiles);

      // 转换上传结果格式，包含文件路径用于Hash计算
      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        const uploadFile = uploadFiles[i];

        this.uploadResults.push({
          componentName: uploadFile.componentName,
          success: result.success,
          cdnUrl: result.cdnUrl,
          buildHash: uploadFile.buildHash,
          filePath: uploadFile.filePath, // 记录文件路径用于Hash计算
          error: result.error
        });
      }

      // 显示上传统计
      const stats = this.cdnUploader.getUploadStats(results);
      console.log(`[Upload] 上传统计:`);
      console.log(`  - 总文件: ${stats.totalFiles}`);
      console.log(`  - 成功: ${stats.successCount}`);
      console.log(`  - 失败: ${stats.failureCount}`);
      console.log(`  - 总大小: ${(stats.totalSize / 1024 / 1024).toFixed(2)}MB`);
      console.log(`  - 平均耗时: ${stats.avgUploadTime.toFixed(0)}ms`);

    } catch (error) {
      console.error('[Upload] 批量上传失败:', error);
      throw new Error('CDN上传失败');
    }
  }



  /**
   * 步骤3: 生成环境配置文件
   */
  generateEnvFiles(): void {
    console.log('📄 生成环境配置文件...');

    const currentTime = new Date().toISOString();
    const buildHash = generateId(8);

    // 生成组件环境数据
    const componentEnvData = this.uploadResults
      .filter(upload => upload.success && upload.cdnUrl)
      .map(upload => ({
        appId: this.appConfig.appId,
        assetId: upload.componentName,
        code: upload.cdnUrl,
        syncCode: this.generateSyncCode(upload.componentName, upload.cdnUrl!),
        currentVersionNo: Math.floor(Math.random() * 100) + 1,
        componentName: upload.componentName,
        buildHash,
        deployTime: currentTime
      }));

    // 生成主环境文件
    const mainEnvPath = path.join(projectRoot, '.airstar_save_code_env.json');
    const mainEnvData = {
      appId: this.appConfig.appId,
      appName: this.appConfig.appName,
      components: componentEnvData,
      deployTime: currentTime,
      buildHash,
      totalComponents: componentEnvData.length,
      buildType: 'vite-umd',
      pipelineVersion: '2.0'
    };

    writeFileSync(mainEnvPath, JSON.stringify(mainEnvData, null, 2));
    console.log(`[Env] ✅ 主环境文件已生成: ${mainEnvPath}`);
    console.log(`[Env] 统一在项目根目录生成配置文件，不在组件目录生成单独文件`);
    console.log(`[Env] 📄 组件环境数据: ${componentEnvData.length} 个`);
    componentEnvData.forEach(comp => {
      console.log(`[Env] 📄 组件环境文件: ${comp.componentName}`);
    });
  }

  /**
   * 生成构建哈希
   */
  private generateBuildHash(cdnUrl: string): string {
    // 从CDN URL中提取目录名作为构建哈希
    const match = cdnUrl.match(/polaris-bui-components-[^-]+-([^/]+)/);
    if (match) {
      return match[1];
    }
    // 如果无法从URL提取，使用简单的哈希算法
    let hash = 0;
    for (let i = 0; i < cdnUrl.length; i++) {
      const char = cdnUrl.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16).substring(0, 8);
  }

  /**
   * 生成syncCode - 完全按照curl格式
   */
  private generateSyncCode(componentName: string, mainJsUrl: string): string {
    // 修复syncUrl生成逻辑，正确处理不同CDN URL格式
    let syncUrl: string;

    if (mainJsUrl.includes('mock-cdn.example.com')) {
      // 对于mock CDN，直接使用原URL（因为是模拟环境）
      syncUrl = mainJsUrl;
    } else if (mainJsUrl.includes('/main.js')) {
      // 对于真实CDN，将 /main.js 替换为 -sync/main.js
      syncUrl = mainJsUrl.replace('/main.js', '-sync/main.js');
    } else {
      // 如果URL格式不符合预期，使用原URL
      syncUrl = mainJsUrl;
    }

    // 从URL中提取hashId，支持不同的URL格式
    let hashId = 'generated';
    try {
      if (mainJsUrl.includes('-')) {
        const urlParts = mainJsUrl.split('-');
        const lastPart = urlParts[urlParts.length - 1];
        if (lastPart) {
          hashId = lastPart.replace('/main.js', '').replace('.js', '');
        }
      } else {
        // 从URL路径中提取唯一标识符
        const urlPath = new URL(mainJsUrl).pathname;
        const pathParts = urlPath.split('/');
        hashId = pathParts[pathParts.length - 2] || 'generated';
      }
    } catch (error) {
      console.warn(`[SyncCode] 无法解析URL获取hashId: ${mainJsUrl}，使用默认值`);
    }

    console.log(`[SyncCode] 组件 ${componentName}: 主URL=${mainJsUrl}, 同步URL=${syncUrl}, hashId=${hashId}`);

    return `new Promise((resolve, reject) => { try { let topWindow = window.Utils && window.Utils.globalWindow; const topWindowInIframe = topWindow && topWindow.parent !== topWindow; if (topWindowInIframe) { console.log('[lux-info]${componentName} in iframe'); topWindow = topWindow.parent; } const lockName = '_${componentName}_Lock'; const eventName = '_${componentName}_LockReleased'; const finalStatusFlag = '_${componentName}_FinalStatus'; let resolved = false; let timeoutId; const checkSuccessAndResolve = () => { if (topWindow[finalStatusFlag] === 'success') { resolved = true; resolve(); return true; } return false; }; const acquireLock = () => { if (!topWindow[lockName]) { topWindow[lockName] = true; return true; } return false; }; const releaseLock = (status) => { topWindow[lockName] = false; const event = new CustomEvent(eventName, { detail: { status } }); topWindow[finalStatusFlag] = status; topWindow.dispatchEvent(event); }; const executeScript = () => { if (checkSuccessAndResolve()) return; const script = document.createElement('script'); script.src = '${syncUrl}'; const startT = performance.now(); const resolveWrapper = (timeout = false) => { if (resolved) { return; } if (checkSuccessAndResolve()) return; const endT = performance.now(); const perfLoss = Math.floor(endT - startT); console.log('[lux-info]${componentName} consumed', perfLoss, 'ms to finish load, timeout:', timeout); resolved = true; if (timeout) { releaseLock('failed'); reject('[lux-info]${componentName} Load timeout'); } else { window.Utils.airstarWeblog.plugins.radar.event({ name: 'airstar-load-custom-components-per-loss', extra_info: { appId: window.Context.appInfo.id, appName: window.Context.appInfo.name, appNameZh: window.Context.appInfo.nameZh, componentName: '${componentName}', perfLoss, }, }); releaseLock('success'); delete topWindow[lockName]; resolve(); } }; window['_${componentName}-${hashId}_resolver'] = resolveWrapper; topWindow['_${componentName}-${hashId}_resolver'] = resolveWrapper; timeoutId = setTimeout(() => resolveWrapper(true), 3000); script.onerror = (e) => { console.error('[lux-info]${componentName} load failed', e); clearTimeout(timeoutId); releaseLock('failed'); reject(e); }; document.head.appendChild(script); }; if (acquireLock()) { executeScript(); } else { console.log('[lux-info]${componentName} Another instance is already running, waiting for lock release.'); const onLockReleased = (event) => { clearTimeout(timeoutId); if (event.detail.status === 'success') { console.log('[lux-info]${componentName} resolve directly'); topWindow.removeEventListener(eventName, onLockReleased); resolve(); } else { console.log('[lux-info]${componentName} acquire lock again'); if (acquireLock()) { topWindow.removeEventListener(eventName, onLockReleased); executeScript(); } } }; topWindow.addEventListener(eventName, onLockReleased); } } catch (error) { console.error('[lux-info]${componentName} 执行垫片逻辑时报错', error); reject(error); } });`;
  }

  /**
   * 步骤4: 调用后台API - 完整的批量上传逻辑
   */
  async callBackendAPI(): Promise<void> {
    if (this.options.skipApi) {
      console.log('⏭️  跳过API调用步骤');
      return;
    }

    if (this.options.dryRun) {
      console.log('⏭️  跳过API调用步骤（干跑模式）');
      this.showDryRunPreview();
      return;
    }

    // 移除mock选项对API调用的影响，让mock只影响CDN上传

    console.log('📡 开始调用后台API批量上传组件...');

    // 准备API请求数据 - 完全按照curl格式
    const successfulUploads = this.uploadResults.filter(result => result.success);

    if (successfulUploads.length === 0) {
      console.warn('[API] 没有成功上传的组件，跳过API调用');
      return;
    }

    // 构建组件数据 - 使用真实的CDN上传结果，增强变更检测
    const componentsData = successfulUploads.map(upload => {
      const assetId = getComponentAssetId(upload.componentName);
      const currentTime = new Date().toISOString();

      // 使用真实的CDN URL，确保不为空
      const realCdnUrl = upload.cdnUrl || `https://fallback-cdn.example.com`;

      // 生成基于时间戳和内容的唯一buildHash，确保每次都能触发变更
      const timestampHash = Date.now().toString(36);
      const contentHash = upload.buildHash || this.generateBuildHash(realCdnUrl);
      const uniqueBuildHash = `${contentHash}-${timestampHash}`;

      // 计算文件Hash（如果有构建文件的话）
      let fileHash = '';
      if (upload.filePath && existsSync(upload.filePath)) {
        fileHash = calculateFileHash(upload.filePath);
        console.log(`[Hash] 组件 ${upload.componentName} 文件Hash: ${fileHash.substring(0, 8)}...`);
      }

      // 参考airstar-material的版本号处理：始终使用0
      // 这是正确的做法，让后台自动管理版本号
      const versionNo = 0;
      console.log(`[Version] 组件 ${upload.componentName} 使用版本号: ${versionNo} (参考airstar-material)`);

      if (assetId) {
        console.log(`[Version] 组件 ${upload.componentName} 有assetId，后台将自动处理版本递增`);
      }

      const componentData: any = {
        appId: this.appConfig.appId,
        code: realCdnUrl, // 使用真实的CDN地址
        syncCode: this.generateSyncCode(upload.componentName, realCdnUrl),
        currentVersionNo: versionNo, // 使用动态版本号确保变更
        componentName: upload.componentName, // 使用componentName而不是name
        name: upload.componentName, // 保持向后兼容
        nameZh: getComponentNameZh(upload.componentName),
        buildHash: uniqueBuildHash, // 使用唯一的构建哈希
        deployTime: currentTime, // 部署时间
        fileHash: fileHash, // 添加文件Hash用于变更检测
      };

      console.log(`[API] ✅ 使用真实CDN: ${realCdnUrl}`);

      // 如果找到assetId，直接包含在请求中，避免409冲突
      if (assetId) {
        componentData.assetId = assetId;
        console.log(`[API] 组件 ${upload.componentName} 使用现有assetId: ${assetId} (更新模式)`);
        console.log(`[API]   📦 更新代码: ${upload.cdnUrl}`);
        console.log(`[API]   🔢 动态版本号: ${componentData.currentVersionNo}`);
        console.log(`[API]   🔨 唯一构建Hash: ${uniqueBuildHash}`);
        console.log(`[API]   📄 文件Hash: ${fileHash.substring(0, 8)}...`);
        console.log(`[API]   🕒 部署时间: ${currentTime}`);
      } else {
        console.log(`[API] 组件 ${upload.componentName} 没有assetId，将创建新资产`);
      }

      return componentData;
    });

    // 获取Git信息用于更新人信息
    const gitBranch = getGitBranch();
    const gitCommitter = getGitCommitter();

    const apiPayload = {
      appId: this.appConfig.appId,
      components: JSON.stringify(componentsData),
      pages: "[]",
      isMasterBranch: 'false' as 'true' | 'false',
      laneId: gitBranch,
      kdevPipelineLogCreator: gitCommitter, // 添加更新人信息
    };

    try {
      console.log(`[API] 准备批量处理 ${componentsData.length} 个组件`);
      const response = await this.apiClient.batchSaveCodeForPipeline(apiPayload);

      // 检查API响应，包括嵌套的状态码
      const isSuccess = response.success && response.data?.data?.status === 200;

      console.log(`[API] 响应检查: success=${response.success}, status=${response.data?.status}, isSuccess=${isSuccess}`);

      if (isSuccess) {
        console.log('[API] ✅ 组件批量处理成功！');
        if (response.data) {
          console.log(`[API] 任务ID: ${response.data.jobId || 'N/A'}`);
          console.log(`[API] 处理组件数: ${response.data.savedComponents || componentsData.length}`);

          // 显示详细的API响应信息 - 修复数据结构解析
          const apiData = response.data.data?.data || response.data.data || response.data;
          console.log(`[API] 🔍 检查API数据结构: created=${apiData.created?.length || 0}, updated=${apiData.updated?.length || 0}`);
          console.log(`[API] 🔍 API数据路径: response.data.data.data`);

          if (apiData.created && apiData.created.length > 0) {
            console.log(`[API] 📝 创建的组件 (${apiData.created.length}个):`);
            apiData.created.forEach((item: any) => {
              console.log(`[API]   - ${item.name}(${item.nameZh}): ${item.assetId}`);
            });
          }
          if (apiData.updated && apiData.updated.length > 0) {
            console.log(`[API] 🔄 更新的组件 (${apiData.updated.length}个):`);
            apiData.updated.forEach((item: any) => {
              console.log(`[API]   - ${item.name}(${item.nameZh}): ${item.assetId}`);

              // 显示我们发送的完整更新数据
              const sentComponent = componentsData.find((comp: any) => comp.name === item.name || comp.componentName === item.name);
              if (sentComponent) {
                console.log(`[API]     📦 更新的代码URL: ${sentComponent.code}`);
                console.log(`[API]     🔢 版本号: ${sentComponent.currentVersionNo}`);
                console.log(`[API]     🏗️ 构建哈希: ${sentComponent.buildHash}`);
                console.log(`[API]     🕒 部署时间: ${sentComponent.deployTime}`);
                console.log(`[API]     🆔 资产ID: ${sentComponent.assetId}`);
              } else {
                console.log(`[API]     ⚠️ 未找到发送的组件数据`);
              }
            });
          }
          if (apiData.viewContent) {
            console.log(`[API] 📋 操作结果: ${apiData.viewContent}`);
          }

          // 步骤5: 生成组件配置文件
          await this.generateComponentConfigs(response.data);
        }
      } else {
        // API调用失败
        console.error('[API] ❌ 组件处理失败:', response.data?.errorMessage || response.message);
        console.error('[API] 完整响应:', JSON.stringify(response, null, 2));
        throw new Error(`API调用失败: ${response.data?.errorMessage || response.message}`);
      }

    } catch (error) {
      console.error('[API] API调用过程中发生错误:', error);
      throw error;
    }
  }
  /**
   * 步骤5: 生成组件配置文件 (const.json)
   */
  private async generateComponentConfigs(apiResponseData: any): Promise<void> {
    console.log('📝 开始生成组件配置文件...');

    try {
      // 检查API响应数据结构
      let createdComponents: any[] = [];
      let updatedComponents: any[] = [];

      console.log('[Config] API响应数据结构:', JSON.stringify(apiResponseData, null, 2));

      // 处理嵌套的响应结构
      if (apiResponseData?.data?.data?.created) {
        createdComponents = apiResponseData.data.data.created;
      } else if (apiResponseData?.data?.created) {
        createdComponents = apiResponseData.data.created;
      } else if (apiResponseData?.created) {
        createdComponents = apiResponseData.created;
      }

      if (apiResponseData?.data?.data?.updated) {
        updatedComponents = apiResponseData.data.data.updated;
      } else if (apiResponseData?.data?.updated) {
        updatedComponents = apiResponseData.data.updated;
      } else if (apiResponseData?.updated) {
        updatedComponents = apiResponseData.updated;
      }

      console.log(`[Config] 发现 ${createdComponents.length} 个新创建的组件`);
      console.log(`[Config] 发现 ${updatedComponents.length} 个更新的组件`);

      // 为新创建的组件生成配置文件
      for (const component of createdComponents) {
        await this.generateSingleComponentConfig(component.name, component.assetId);
      }

      // 为更新的组件检查并生成配置文件（如果不存在）
      for (const component of updatedComponents) {
        await this.ensureComponentConfig(component.name, component.assetId);
      }

      console.log('[Config] ✅ 组件配置文件生成完成！');

    } catch (error) {
      console.error('[Config] ❌ 生成配置文件失败:', error);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 生成单个组件的const.json配置文件
   */
  private async generateSingleComponentConfig(componentName: string, assetId: string): Promise<void> {
    try {
      const component = this.components.find(c => c.name === componentName);
      if (!component) {
        console.warn(`[Config] 未找到组件 ${componentName} 的信息，跳过配置文件生成`);
        return;
      }

      const configPath = path.join(component.path, 'const.json');

      // 简化的const.json格式，主要包含appId和assetId
      const config = {
        appId: this.appConfig.appId,
        appName: this.appConfig.appName,
        assetId: assetId,
        remoteEntry: "",
        publicPath: "https://cdnfile.corp.kuaishou.com/kc/files/a/airstar",
        dependencies: JSON.stringify({
          "react": "^18.0.0",
          "react-dom": "^18.0.0",
          "@types/react": "^18.0.0",
          "@types/react-dom": "^18.0.0",
          "vue": "^3.2.37"
        }),
        componentInfo: {
          name: componentName,
          type: "0"
        },
        devMode: "3",
        templateType: "react18",
        storeInfo: {},
        shared: [
          { "react": { "requiredVersion": "^18.0.0", "singleton": true } },
          { "react-dom": { "requiredVersion": "^18.0.0", "singleton": true } },
          { "@types/react": { "requiredVersion": "^18.0.0", "singleton": true } },
          { "@types/react-dom": { "requiredVersion": "^18.0.0", "singleton": true } },
          { "vue": { "requiredVersion": "^3.2.37", "singleton": true } }
        ]
      };

      writeFileSync(configPath, JSON.stringify(config, null, 2));
      console.log(`[Config] ✅ 创建配置文件: ${configPath}`);
      console.log(`[Config] 📋 组件: ${componentName}, AppId: ${this.appConfig.appId}, AssetId: ${assetId}`);

    } catch (error) {
      console.error(`[Config] ❌ 生成组件 ${componentName} 配置文件失败:`, error);
    }
  }

  /**
   * 确保组件配置文件存在（如果不存在则创建）
   */
  private async ensureComponentConfig(componentName: string, assetId: string): Promise<void> {
    try {
      const component = this.components.find(c => c.name === componentName);
      if (!component) {
        console.warn(`[Config] 未找到组件 ${componentName} 的信息，跳过配置文件检查`);
        return;
      }

      const configPath = path.join(component.path, 'const.json');

      // 如果配置文件不存在，则创建
      if (!existsSync(configPath)) {
        await this.generateSingleComponentConfig(componentName, assetId);
      } else {
        console.log(`[Config] ✅ 配置文件已存在: ${configPath}`);
      }

    } catch (error) {
      console.error(`[Config] ❌ 检查组件 ${componentName} 配置文件失败:`, error);
    }
  }







  /**
   * 显示干跑模式预览
   */
  private showDryRunPreview(): void {
    const successfulUploads = this.uploadResults.filter(result => result.success);

    if (successfulUploads.length === 0) {
      console.log('[API] 🧪 干跑模式：没有成功上传的组件');
      return;
    }

    console.log('[API] 🧪 干跑模式，API调用数据预览:');
    console.log(`[API] 应用ID: ${this.appConfig.appId}`);
    console.log(`[API] 组件数量: ${successfulUploads.length}`);
    successfulUploads.forEach(upload => {
      console.log(`  - ${upload.componentName}: ${upload.cdnUrl}`);
    });
  }

  /**
   * 执行完整流程
   */
  async run(): Promise<void> {
    console.log('🚀 开始Polaris BUI Components Pipeline流程...');
    console.log(`[Pipeline] 项目: ${this.appConfig.appName}`);
    console.log(`[Pipeline] App ID: ${this.appConfig.appId}`);
    console.log(`[Pipeline] 构建模式: UMD`);

    if (this.options.dryRun) {
      console.log(`[Pipeline] 🧪 干跑模式启用`);
    }

    try {
      // 步骤0: 扫描组件
      this.scanComponents();

      // 步骤1: 构建
      await this.runBuild();

      // 步骤2: 上传
      await this.uploadToCDN();

      // 步骤3: 生成环境文件
      this.generateEnvFiles();

      // 步骤4: 调用API
      await this.callBackendAPI();

      // 输出总结
      this.printSummary();

    } catch (error) {
      console.error('\n💥 Pipeline执行失败:', error);
      process.exit(1);
    }
  }

  /**
   * 打印执行总结
   */
  private printSummary(): void {
    console.log('\n🎉 Pipeline执行完成！');
    console.log('='.repeat(50));

    console.log(`📊 构建结果:`);
    console.log(`  - 总组件数: ${this.buildResults.length}`);
    console.log(`  - 构建成功: ${this.buildResults.filter(r => r.success).length}`);
    console.log(`  - 构建失败: ${this.buildResults.filter(r => !r.success).length}`);

    console.log(`📤 上传结果:`);
    console.log(`  - 上传成功: ${this.uploadResults.filter(r => r.success).length}`);
    console.log(`  - 上传失败: ${this.uploadResults.filter(r => !r.success).length}`);

    console.log(`📄 环境文件:`);
    const successfulUploads = this.uploadResults.filter(r => r.success);
    console.log(`  - 组件环境数据: ${successfulUploads.length} 个`);

    if (successfulUploads.length > 0) {
      console.log('\n📋 成功部署的组件:');
      successfulUploads.forEach(upload => {
        console.log(`  - ${upload.componentName}: ${upload.cdnUrl}`);
      });
    }

    console.log('\n✨ 所有流程已完成！');
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);

  // 帮助信息
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🚀 Polaris BUI Components Pipeline 使用说明:

  tsx scripts/vite-pipeline.ts                                   # 完整UMD流程
  tsx scripts/vite-pipeline.ts --dry-run                         # 干跑模式
  tsx scripts/vite-pipeline.ts --filter=table                    # 过滤组件名
  tsx scripts/vite-pipeline.ts --types=table,service             # 过滤组件类型
  tsx scripts/vite-pipeline.ts --skip-build                      # 跳过构建
  tsx scripts/vite-pipeline.ts --skip-upload                     # 跳过上传
  tsx scripts/vite-pipeline.ts --skip-api                        # 跳过API调用

参数说明:
  --filter=<pattern>      按名称过滤组件（正则表达式）
  --types=<types>         按类型过滤组件: base,service,table,filter,complex
  --skip-build           跳过构建步骤，使用现有构建产物
  --skip-upload          跳过上传步骤，生成模拟CDN URLs
  --skip-api             跳过API调用步骤
  --dry-run              干跑模式，跳过API调用（构建和上传正常执行）
  --mock                 模拟模式，显示API调用预览但不真实调用
  --branch=<name>        指定Git分支名称
  --commit-id=<id>       指定Git提交ID
  --commit-author=<author> 指定提交作者

示例:
  npm run pipeline                                              # 默认UMD完整流程
  npm run pipeline:umd                                          # UMD完整流程
  npm run pipeline:esm                                          # ESM完整流程
  npm run pipeline:esm -- --dry-run                             # ESM干跑模式（只构建，不上传）
  npm run pipeline:umd -- --dry-run                             # UMD干跑模式（只构建，不上传）
  npm run pipeline:esm -- --skip-upload                         # ESM跳过上传（构建但不上传）
  npm run pipeline:esm -- --filter=table                        # ESM构建特定组件
  npm run pipeline:esm -- --types=service --dry-run             # ESM构建服务组件（干跑）
    `);
    return;
  }

  // 解析参数
  const options: PipelineOptions = {
    skipBuild: args.includes('--skip-build'),
    skipUpload: args.includes('--skip-upload'),
    skipApi: args.includes('--skip-api'),
    dryRun: args.includes('--dry-run'),
    mock: args.includes('--mock'),
  };

  // 解析过滤器
  const filterArg = args.find(arg => arg.startsWith('--filter='));
  if (filterArg) {
    options.filter = filterArg.split('=')[1];
  }

  // 解析组件名称
  const componentNames = args.filter(arg =>
    !arg.startsWith('--') && arg !== 'vite-pipeline.ts'
  );
  if (componentNames.length > 0) {
    options.components = componentNames;
  }

  // 执行Pipeline
  const pipeline = new PolarisVitePipeline(options);
  await pipeline.run();
}

// 运行脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('[Pipeline] 脚本执行失败:', error);
    process.exit(1);
  });
}

export { PolarisVitePipeline };