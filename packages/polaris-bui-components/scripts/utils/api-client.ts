/**
 * API客户端工具 - 用于调用后台服务API
 * 基于airstar-material的实现，适配polaris-bui-components
 */

import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import fetch from 'node-fetch';
const { existsSync, readFileSync, readdirSync } = fs;

// 组件数据接口 - 完整的API格式，支持assetId
interface ComponentData {
  appId: string;
  assetId?: string; // 添加assetId支持，用于组件已存在时的变更
  code: string;
  syncCode: string;
  currentVersionNo: number;
  name: string;
  nameZh: string;
  componentName?: string; // 添加componentName字段
  buildHash?: string; // 添加buildHash字段
  deployTime?: string; // 添加deployTime字段
  fileHash?: string; // 添加fileHash字段
}

// API请求体格式
interface APIPayload {
  appId: string;
  components: string; // JSON字符串化的ComponentData[]
  pages: string;
  isMasterBranch: string;
  laneId: string;
  kdevPipelineLogCreator?: string; // 添加更新人信息字段
}

interface APIResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  code?: number;
}

// 认证信息 - 完全基于curl文件更新 (2025年7月25日)
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyTmFtZSI6ImRlbmd5b3VuYW4iLCJpYXQiOjE3NTE2MzM2MzZ9.m69O-qVhExsSpqcwpFQ5AQQpIUyIMuwa876wpnGCYE8';

const COOKIE_STRING = 'pa-gateway-token=e318067c-929c-4ef9-b7f7-3cc87324d97b; amc-token-host=airstar-amc.corp.kuaishou.com; apdid=6aa14fdc-1421-411c-9580-75eda4dacb6da032a1285f6735d3ecf295f4f90649b7:1753077922:1; did=web_f744280ca601743363b00c2ab3d22039699f; hdige2wqwoino=SJcdtpRyBXdz5w76FraknAtntasAefZM8596172e; soft_did=1619580708547; Airstar-userInfo-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySW5mbyI6eyJuYW1lIjoiZGVuZ3lvdW5hbiIsImRpc3BsYXlOYW1lIjoi6YKT5oKg5Y2XIiwibWFpbCI6ImRlbmd5b3VuYW5Aa3VhaXNob3UuY29tIiwiYXZhdGFyIjoiaHR0cHM6Ly9zdGF0aWMueXhpbWdzLmNvbS91ZGF0YS9wa2cvS1MtSVMtQVZBVEFSLVBST0RVQ1RJT04vcHJlLWUzNDk3MTg2NjI3OTQwMzdiMmRiYWJiNzE0NjYzMGVlLzQ0NDA3MkNEODYwNTBDNzgxNUQwNzI5QjM5QTEzMkRCX2NvbXByZXNzZWRfMTAwLmpwZyIsImRlcGFydG1lbnQiOiLkuLvnq5nmioDmnK_pg6giLCJuYW1lWmgiOiLpgpPmgqDljZcifSwiaWF0IjoxNzUzMDgzNzU3fQ.TJiSKTFYkA7evxfhZIyer0X5shiiCdF5UIBLGIFUMeU; polaris_tk_prod=NELZBf582NTbWDF+KcYL/EfS1pawjsXswbEQ8PenuBM1AMGKFtf2XCLImRGzgDVxAxQh5q5KHecK7fTy1Oj7GhOZNU6vXwMX0P0pcolTaa8jf2b5/VZ9b8JzZV6Qx16W; weblogger_did=web_138842851576B388; bUserId=1000255816241; userId=72206456; kuaishou.shop.ideat_st=ChZrdWFpc2hvdS5zaG9wLmlkZWF0LnN0EsAB3LN9HWmEOyjjlEzKg8YfVJ1hylEmDpfnyd9Dnq4FPARxrMcw2GzRCjInqLzuITsOxDRFyO03ZBxKSXF96K4juwpQ_s9Sn2ZcMQaN2MmeYczE1ez-AMsJlQY_5oovLmKN4VMCSAUlh4d0XjqvwymerEqndgN9TKHJCdKxC_YJ_gxf0lQApt6WTgCc3iC2EBmcx5B6bVq-aKuq3vcV6kTvoXdbvy2tFT39ggS1qZLtKDl7jp4l6OmRrzMP4EixH05uGhJ1qtbF_gkmYl7CzKhbuG3g1nYiIBjPIe2orEKO8EIYFIh5MV7fw66DUI1kGLztTR3MCmTgKAUwAQ; kuaishou.shop.ideat_ph=a770ecf691f5c70fe421f1c8228d3a51b541; ehid=6500cwRKJZ8ZbZlb04sd2q8Fsmx2GBUmaiYM9; accessproxy_session=2d6a4129-c5d0-4c61-8bff-ce0dbef568ba; ktrace-context=1|MS43MzY3Njk0NTcyODQ3MTY3LjQ2NjU0MTYxLjE3NTMxNTQzNzIxNDQuMTQ1NzYwOQ==|MS43MzY3Njk0NTcyODQ3MTY3LjQ1NTMzNjQzLjE3NTMxNTQzNzIxNDQuMTQ1NzYxMA==|0|webservice-htgc|operation|true|src-Js; apdid=e04a1c0e-5808-4f68-83b6-0016d5649fb4de0c55df68c372384440c8f070001b6e:1745554667:1; accessproxy_session=204193f4-f5d0-49e3-8341-dac7f03ebee7';

/**
 * 读取组件配置文件获取assetId
 * 参考airstar-material的实现方式，增强错误处理和日志
 */
function readComponentConfig(componentName: string): any | null {
  try {
    const configPath = path.join(process.cwd(), 'components', componentName, 'const.json');
    console.log(`[Config] 尝试读取组件配置: ${configPath}`);

    if (existsSync(configPath)) {
      const configContent = readFileSync(configPath, 'utf-8');
      const config = JSON.parse(configContent);
      console.log(`[Config] 成功读取组件 ${componentName} 配置，assetId: ${config.assetId || '未找到'}`);
      return config;
    }

    // 如果没有const.json，尝试查找其他配置文件
    const componentDir = path.join(process.cwd(), 'components', componentName);
    if (existsSync(componentDir)) {
      const files = readdirSync(componentDir);
      const configFiles = files.filter(f => f.includes('config') || f.includes('const'));
      if (configFiles.length > 0) {
        console.warn(`[Config] 组件 ${componentName} 找到其他配置文件: ${configFiles.join(', ')}`);

        // 尝试读取第一个配置文件
        for (const configFile of configFiles) {
          try {
            const altConfigPath = path.join(componentDir, configFile);
            const altConfigContent = readFileSync(altConfigPath, 'utf-8');
            const altConfig = JSON.parse(altConfigContent);
            if (altConfig.assetId) {
              console.log(`[Config] 从 ${configFile} 中找到assetId: ${altConfig.assetId}`);
              return altConfig;
            }
          } catch (altError) {
            console.warn(`[Config] 无法解析配置文件 ${configFile}:`, altError);
          }
        }
      }
    }

    console.warn(`[Config] 组件 ${componentName} 的配置文件不存在或无assetId，将创建新资产`);
    return null;
  } catch (error) {
    console.error(`[Config] 读取组件 ${componentName} 配置失败:`, error);
    return null;
  }
}

/**
 * 获取组件的assetId
 * 如果配置文件中有assetId则使用，否则返回空字符串让API创建新资产
 */
function getComponentAssetId(componentName: string): string {
  const config = readComponentConfig(componentName);

  if (config?.assetId) {
    console.log(`[AssetId] 组件 ${componentName} 使用配置的assetId: ${config.assetId}`);
    return config.assetId;
  } else {
    console.warn(`[AssetId] 组件 ${componentName} 未找到assetId，将让API创建新资产`);
    return ''; // 返回空字符串，让API创建新资产
  }
}

/**
 * 组件中文名称映射表
 * 基于curl示例中的成功组件映射
 */
const COMPONENT_NAME_ZH_MAPPING: Record<string, string> = {
  'polaris-author-flow-service': '流量塑形容器组件',
  'polaris-complex-filter-v1': 'polaris-complex-filter-v1',
  'polaris-co-table-author-flow': '璇玑通用表格组件',
  'polaris-easy-form-filter-author-flow': '简易form表单组件',
  'polaris-ks-table': 'polaris-ks-table',
  'polaris-middleware': 'polaris-middleware',
  'base-middleware': 'polaris-middleware', // base组件映射为middleware
  'polaris-service-insight-content-realtime': 'polaris-service-insight-content-realtime',
  'polaris-service-realtime-video-ranking': '实时视频榜单服务组件',
  'polaris-tabs-author-flow': '璇玑标签选择器',

  // polaris-bui-components 实际组件映射
  'polaris-co-table-rank': '璇玑排名表格组件',
  'polaris-table-biz-manager-service': '业务管理表格服务组件',
  'polaris-flow-private-table': '流量私域表格组件',
  'polairs-author-info': '作者信息组件',
  'polaris-service-template': '服务模板组件',
  'polaris-service-similar-author': '相似作者服务组件',
  'lux-tabs': 'Lux标签页组件',
  'polaris-text-hover': '文本悬浮组件',
  'polaris-service-complex-filter-ks-table-template': '复杂筛选表格模板服务组件',
  'polaris-service-push-operation': '推送操作服务组件',
  'polaris-card-omit-v0': '卡片省略组件v0',
  'easy-form-filter-author-flow': '简易表单筛选作者流组件',
  'user-ratio': '用户比例组件',
  'easy-form-filter-v1': '简易表单筛选组件v1',
  'polaris-css': 'Polaris样式组件',
  'polaris-photo-scroll-list': '图片滚动列表组件',
  'polaris-service-operation-order-author': '操作订单作者服务组件',
  'polaris-tabs': '标签页组件',
  'polaris-service-target-manager': '目标管理服务组件',
  'polaris-co-sort-v0': '璇玑排序组件v0',
  'polaris-co-table-v1': '璇玑表格组件v1',
  'polaris-co-table': '璇玑表格组件',
  'easy-form-filter': '简易表单筛选组件',
  'polaris-flow-tpl-percent': '流量百分比组件',
  'polaris-complex-filter': '复杂筛选组件',
  'polaris-date-picker': '日期选择器',
  'polaris-service-target-create-manager': '目标创建管理服务组件',
  'amis-render': 'Amis渲染组件',
  'polaris-co-tooltip-v0': '璇玑提示框组件v0',
  'polaris-ks-table-boost-author': 'KS表格提升作者组件',
  'polaris-service-topic-exploration': '话题探索服务组件',
  'polaris-service-potential-author': '潜力作者服务组件',
  'polaris-service-open-reason': '开放原因服务组件',
  'polaris-service-execution-manager': '执行管理服务组件',
  'polaris-service-easyfilter-table': '简易筛选表格服务组件',
  'polaris-service-category-lock': '类目锁定服务组件',
  'polaris-season-search-topic-service': '季度搜索话题服务组件',
  'polaris-season-content-topic-service': '季度内容话题服务组件',
  'polaris-ks-table-operation-order-author-table': 'KS表格操作订单作者表格组件',
  'polaris-ks-table-author-level': 'KS表格作者等级组件',
  'polaris-creator-screening-v0': '创作者筛选组件v0',
  'polaris-common-filter-v0': '通用筛选组件v0',
  'polaris-chart': '图表组件',
  'polaris-button-tabs': '按钮标签页组件',

  // 其他常见组件映射
  'polaris-table': '表格组件',
  'polaris-form': '表单组件',
  'polaris-filter': '筛选组件'
};

/**
 * 获取组件中文名称
 */
export function getComponentNameZh(componentName: string): string {
  return COMPONENT_NAME_ZH_MAPPING[componentName] || componentName;
}

export class PolarisAPIClient {
  private baseURL: string;
  private timeout: number;
  private retryCount: number;
  private laneId: string;

  constructor(
    baseURL: string = 'https://airstar.corp.kuaishou.com',
    timeout: number = 30000,
    retryCount: number = 3,
    laneId: string = 'test_only_az3'
  ) {
    this.baseURL = baseURL;
    this.timeout = timeout;
    this.retryCount = retryCount;
    this.laneId = laneId;
  }

  /**
   * 设置泳道ID
   */
  setLaneId(laneId: string): void {
    this.laneId = laneId;
    console.log(`[API] 泳道已设置为: ${laneId}`);
  }

  /**
   * 执行HTTP请求的基础方法
   */
  private async request<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any,
    headers?: Record<string, string>
  ): Promise<APIResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;

    // 完全按照curl文件的请求头格式，调整顺序
    const requestConfig: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Cookie': COOKIE_STRING,
        'trace-context': `{"laneId": "${this.laneId}"}`, // 使用动态配置的laneId
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        ...headers,
      },
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      requestConfig.body = JSON.stringify(data);
    }

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);
    requestConfig.signal = controller.signal;

    try {
      console.log(`[API] ${method} ${url}`);
      console.log(`[API] 请求头:`, JSON.stringify(requestConfig.headers, null, 2));
      if (data) {
        console.log(`[API] 请求数据:`, JSON.stringify(data, null, 2));
      }

      const response = await fetch(url, requestConfig as any);
      clearTimeout(timeoutId);

      const responseText = await response.text();
      console.log(`[API] 响应状态: ${response.status} ${response.statusText}`);
      console.log(`[API] 原始响应:`, responseText.substring(0, 500) + (responseText.length > 500 ? '...' : ''));

      // 检查是否返回了HTML页面（404错误或认证失败）
      if (responseText.includes('<!DOCTYPE html>') || responseText.includes('<html')) {
        console.error(`[API] 服务器返回HTML页面，可能是404或认证失败`);
        return {
          success: false,
          message: `API端点不存在或认证失败 (${response.status})`,
          code: response.status
        };
      }

      // 尝试解析JSON
      let result: any;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error(`[API] JSON解析失败:`, parseError);
        console.error(`[API] 响应不是有效JSON，内容:`, responseText.substring(0, 1000));
        return {
          success: false,
          message: `服务器返回非JSON响应: ${responseText.substring(0, 200)}`,
          code: response.status
        };
      }

      if (!response.ok) {
        console.error(`[API] HTTP错误: ${response.status} ${response.statusText}`);
        return {
          success: false,
          message: result.message || `HTTP ${response.status}: ${response.statusText}`,
          code: response.status
        };
      }

      console.log(`[API] 解析后响应:`, JSON.stringify(result, null, 2));

      return {
        success: true,
        data: result,
        message: result.message,
        code: result.code,
      };

    } catch (error) {
      clearTimeout(timeoutId);
      console.error(`[API] 请求失败:`, error);

      return {
        success: false,
        message: error.message,
      };
    }
  }

  /**
   * 带重试机制的请求方法
   */
  private async requestWithRetry<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any,
    headers?: Record<string, string>
  ): Promise<APIResponse<T>> {
    let lastError: Error = new Error('未知错误');

    for (let attempt = 1; attempt <= this.retryCount; attempt++) {
      try {
        const result = await this.request<T>(endpoint, method, data, headers);

        if (result.success) {
          return result;
        }

        // 如果是客户端错误（4xx），不重试
        if (result.code && result.code >= 400 && result.code < 500) {
          return result;
        }

        lastError = new Error(result.message || '请求失败');

      } catch (error) {
        lastError = error as Error;
      }

      if (attempt < this.retryCount) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`[API] 重试 ${attempt}/${this.retryCount}，${delay}ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    return {
      success: false,
      message: `请求失败，已重试${this.retryCount}次: ${lastError.message}`,
    };
  }

  /**
   * 批量保存或创建组件代码到Pipeline
   * 完全按照curl格式构造请求
   */
  async batchSaveCodeForPipeline(payload: APIPayload): Promise<APIResponse> {
    console.log(`[API] 调用批量保存组件代码API...`);
    console.log(`[API] 应用ID: ${payload.appId}`);
    console.log(`[API] 组件数量: ${JSON.parse(payload.components).length}`);
    console.log(`[API] 分支: ${payload.laneId}`);
    console.log(`[API] 是否主分支: ${payload.isMasterBranch}`);
    console.log(`[API] 更新人: ${payload.kdevPipelineLogCreator || '未指定'}`);

    // 使用传入的参数，保留更新人信息
    const modifiedPayload = {
      ...payload,
      appId: payload.appId, // 使用传入的appId
      laneId: payload.laneId || 'feat_p2p_ai_0625', // 使用传入的laneId或默认值
      kdevPipelineLogCreator: payload.kdevPipelineLogCreator // 保留更新人信息
    };

    // 检查是否有组件包含assetId，如果有，直接使用更新模式
    const components = JSON.parse(modifiedPayload.components);
    const hasExistingAssets = components.some((comp: any) => comp.assetId);

    if (hasExistingAssets) {
      console.log(`[API] 检测到现有资产，直接使用更新模式...`);
      return this.batchUpdateCodeForPipeline(modifiedPayload);
    }

    try {
      const result = await this.requestWithRetry(
        '/api/asset/batchSaveOrCreateCodeForPipeline',
        'POST',
        modifiedPayload,
        {
          'trace-context': `{"laneId": "${this.laneId}"}`, // 使用动态配置的laneId
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AUTH_TOKEN}`,
          'Cookie': COOKIE_STRING,
        }
      );

      // 处理422错误（资源已创建）- 调用变更接口
      if (!result.success && result.code === 422) {
        console.log(`[API] 检测到422错误，资源已存在，尝试调用变更接口...`);
        return this.batchUpdateCodeForPipeline(modifiedPayload);
      }

      return result;
    } catch (error: any) {
      console.error(`[API] 批量保存失败:`, error);
      return {
        success: false,
        message: `批量保存失败: ${error.message}`,
        code: 500
      };
    }
  }

  /**
   * 批量更新组件代码到Pipeline（处理422错误时调用）
   * 以变更的形式传输code信息
   */
  async batchUpdateCodeForPipeline(payload: APIPayload): Promise<APIResponse> {
    console.log(`[API] 调用批量更新组件代码API（变更模式）...`);
    console.log(`[API] 应用ID: ${payload.appId}`);
    console.log(`[API] 组件数量: ${JSON.parse(payload.components).length}`);

    // 解析组件数据，确保code以变更形式传输
    const components = JSON.parse(payload.components);
    const updatedComponents = components.map((comp: any) => ({
      ...comp,
      // 确保code信息以变更形式展示 - 这是关键！
      code: comp.code,
      syncCode: comp.syncCode,
      // 保持原有版本号，不要再次增加
      currentVersionNo: comp.currentVersionNo,
      // 确保assetId存在
      assetId: comp.assetId,
      // 标记为更新操作
      operation: 'update'
    }));

    console.log(`[API] 更新组件详细信息:`);
    updatedComponents.forEach((comp: any) => {
      console.log(`[API]   - 组件: ${comp.name}`);
      console.log(`[API]     assetId: ${comp.assetId}`);
      console.log(`[API]     新code: ${comp.code}`);
      console.log(`[API]     版本号: ${comp.currentVersionNo}`);
    });

    const updatePayload = {
      ...payload,
      components: JSON.stringify(updatedComponents),
      // 标记为更新操作
      operationType: 'update'
    };

    // 直接使用通用API端点，因为专门的更新端点不存在
    console.log(`[API] 使用通用API端点进行更新...`);

    return this.requestWithRetry(
      '/api/asset/batchSaveOrCreateCodeForPipeline',
      'POST',
      updatePayload,
      {
        'trace-context': `{"laneId": "${this.laneId}"}`,
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Cookie': COOKIE_STRING,
      }
    );
  }





  /**
   * 创建模拟客户端（用于测试）
   */
  static createMockClient(): PolarisAPIClient {
    const mockClient = new PolarisAPIClient('https://mock-api.example.com');

    // 重写request方法以返回模拟数据
    (mockClient as any).request = async function <T>(
      endpoint: string,
      method: string,
      data?: any
    ): Promise<APIResponse<T>> {
      console.log(`[Mock API] ${method} ${endpoint}`);

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

      // 根据不同端点返回不同的模拟数据
      switch (endpoint) {
        case '/api/asset/batchSaveOrCreateCodeForPipeline':
          const components = JSON.parse(data?.components || '[]');
          return {
            success: true,
            message: 'Components saved successfully',
            data: {
              savedComponents: components.length,
              timestamp: new Date().toISOString(),
              jobId: `job_${Date.now()}`,
              components: components.map(comp => ({
                name: comp.name,
                status: 'success'
              }))
            },
          } as APIResponse<T>;



        default:
          return {
            success: true,
            data: { message: 'Mock response' },
          } as APIResponse<T>;
      }
    };

    return mockClient;
  }
}

/**
 * 创建API客户端的便捷函数
 */
export function createPolarisAPIClient(useMock: boolean = false): PolarisAPIClient {
  if (useMock) {
    console.log('[API] 使用模拟API客户端');
    return PolarisAPIClient.createMockClient();
  }

  console.log('[API] 使用真实API客户端');
  const baseURL = process.env.POLARIS_API_BASE_URL || 'https://airstar.corp.kuaishou.com';
  return new PolarisAPIClient(baseURL);
}

/**
 * 格式化API错误信息
 */
export function formatAPIError(response: APIResponse): string {
  if (response.success) {
    return '';
  }

  let errorMsg = response.message || '未知错误';

  if (response.code) {
    errorMsg = `[${response.code}] ${errorMsg}`;
  }

  return errorMsg;
}

/**
 * 验证批量保存请求数据
 */
export function validateBatchSavePayload(payload: APIPayload): boolean {
  if (!payload.appId || typeof payload.appId !== 'string') {
    console.error('❌ appId 必须是非空字符串');
    return false;
  }

  if (!payload.components || typeof payload.components !== 'string') {
    console.error('❌ components 必须是JSON字符串');
    return false;
  }

  try {
    const components = JSON.parse(payload.components);
    if (!Array.isArray(components)) {
      console.error('❌ components 必须是数组的JSON字符串');
      return false;
    }

    for (const comp of components) {
      if (!comp.appId || !comp.code || !comp.name) {
        console.error('❌ 组件必须包含 appId、code、name 字段');
        return false;
      }
    }
  } catch (error) {
    console.error('❌ components 不是有效的JSON字符串');
    return false;
  }

  if (!payload.pages || typeof payload.pages !== 'string') {
    console.error('❌ pages 必须是JSON字符串');
    return false;
  }

  if (!payload.isMasterBranch || (payload.isMasterBranch !== 'true' && payload.isMasterBranch !== 'false')) {
    console.error('❌ isMasterBranch 必须是 "true" 或 "false"');
    return false;
  }

  if (!payload.laneId || typeof payload.laneId !== 'string') {
    console.error('❌ laneId 必须是非空字符串');
    return false;
  }

  return true;
}

export { getComponentAssetId };