#!/usr/bin/env node

/**
 * 测试组件更新功能
 * 专门测试有assetId的组件是否能成功更新code
 */

import { execSync } from 'node:child_process';
import { readFileSync } from 'node:fs';
import * as path from 'node:path';

// 测试组件列表（这些组件有assetId）
const TEST_COMPONENTS = [
  'polaris-button-tabs',
  'lux-tabs',
  'polaris-css'
];

function getComponentAssetId(componentName: string): string | null {
  try {
    const configPath = path.join(process.cwd(), 'components', componentName, 'const.json');
    const config = JSON.parse(readFileSync(configPath, 'utf-8'));
    return config.assetId || null;
  } catch (error) {
    return null;
  }
}

function testComponentUpdate(componentName: string) {
  console.log(`\n🧪 测试组件更新: ${componentName}`);

  const assetId = getComponentAssetId(componentName);
  if (!assetId) {
    console.log(`❌ 组件 ${componentName} 没有assetId，跳过测试`);
    return;
  }

  console.log(`✅ 组件 ${componentName} 有assetId: ${assetId}`);

  try {
    // 运行构建和上传
    console.log(`🔨 开始构建和上传组件 ${componentName}...`);

    const command = `npx tsx scripts/vite-pipeline.ts --component ${componentName}`;
    console.log(`执行命令: ${command}`);

    const output = execSync(command, {
      encoding: 'utf-8',
      stdio: 'pipe',
      timeout: 120000 // 2分钟超时
    });

    console.log(`✅ 组件 ${componentName} 构建和上传成功`);

    // 检查输出中是否包含更新成功的信息
    if (output.includes('更新的组件')) {
      console.log(`🎉 组件 ${componentName} 更新成功！`);

      // 提取新的CDN URL
      const cdnUrlMatch = output.match(/更新代码: (https:\/\/[^\s]+)/);
      if (cdnUrlMatch) {
        console.log(`📦 新的CDN URL: ${cdnUrlMatch[1]}`);
      }

      // 提取版本号
      const versionMatch = output.match(/版本号: (\d+)/);
      if (versionMatch) {
        console.log(`🔢 新版本号: ${versionMatch[1]}`);
      }

    } else if (output.includes('创建的组件')) {
      console.log(`📝 组件 ${componentName} 被创建（可能assetId无效）`);
    } else {
      console.log(`⚠️ 组件 ${componentName} 处理结果不明确`);
    }

  } catch (error) {
    console.error(`❌ 组件 ${componentName} 测试失败:`, error.message);

    // 如果有输出，显示部分输出用于调试
    if (error.stdout) {
      console.log(`📋 部分输出:`, error.stdout.substring(0, 500));
    }
    if (error.stderr) {
      console.log(`📋 错误输出:`, error.stderr.substring(0, 500));
    }
  }
}

function main() {
  console.log('🚀 开始测试组件更新功能...\n');
  console.log(`📋 测试组件列表: ${TEST_COMPONENTS.join(', ')}`);

  // 检查所有测试组件的assetId
  console.log('\n📋 组件assetId检查:');
  TEST_COMPONENTS.forEach(componentName => {
    const assetId = getComponentAssetId(componentName);
    if (assetId) {
      console.log(`✅ ${componentName}: ${assetId}`);
    } else {
      console.log(`❌ ${componentName}: 无assetId`);
    }
  });

  // 选择一个有assetId的组件进行测试
  const testComponent = TEST_COMPONENTS.find(name => getComponentAssetId(name));

  if (!testComponent) {
    console.log('\n❌ 没有找到有assetId的组件，无法进行测试');
    return;
  }

  console.log(`\n🎯 选择组件 ${testComponent} 进行更新测试`);
  testComponentUpdate(testComponent);

  console.log('\n📋 测试完成！');
  console.log('\n💡 如果更新成功，请检查前端页面是否显示了新的CDN URL');
  console.log('💡 如果前端仍显示旧URL，说明后台API可能需要额外的参数或不同的调用方式');
}

// 运行测试
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const isMainModule = process.argv[1] === __filename;

if (isMainModule) {
  main();
}
